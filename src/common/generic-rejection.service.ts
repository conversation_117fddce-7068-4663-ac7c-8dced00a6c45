import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'

export interface RejectFieldInput {
    entityId: string
    entityType: string
    fieldName: string
    rejectReason: string
    userId: string
}

export interface RejectFieldResponse {
    success: boolean
    entityId: string
    entityType: string
    fieldName: string
    message: string
}

@Injectable()
export class GenericRejectionService {
    constructor(private readonly prisma: PrismaService) {}

    async rejectField(input: RejectFieldInput): Promise<RejectFieldResponse> {
        const { entityId, entityType, fieldName, rejectReason, userId } = input

        // Map of entity types to their Prisma model names
        const entityModelMap: Record<string, string> = {
            // Non-certified entities
            personalInfo: 'personalInfo',
            partnerInfo: 'partnerInfo',
            address: 'address',
            child: 'child',
            employmentInfo: 'employmentInfo',
            pensionInfo: 'pensionInfo',
            salaryEntry: 'salaryEntry',
            // Certified entities (for compatibility)
            certifiedData: 'certifiedData',
            certifiedPensionInfo: 'certifiedPensionInfo',
            certifiedEmploymentInfo: 'certifiedEmploymentInfo',
            certifiedPersonalInfo: 'certifiedPersonalInfo',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYear',
            certifiedPensionCorrections: 'certifiedPensionCorrections',
            certifiedVoluntaryContributions: 'certifiedVoluntaryContributions',
            certifiedPensionParameters: 'certifiedPensionParameters',
            certifiedAddress: 'certifiedAddress',
            certifiedChild: 'certifiedChild',
            certifiedPartnerInfo: 'certifiedPartnerInfo',
            certifiedSalaryEntry: 'certifiedSalaryEntry',
        }

        // Map of entity types to their foreign key field names in CertificationRejectReason
        const foreignKeyMap: Record<string, string> = {
            // Non-certified entities
            personalInfo: 'personalInfoId',
            partnerInfo: 'partnerInfoId',
            address: 'addressId',
            child: 'childId',
            employmentInfo: 'employmentInfoId',
            pensionInfo: 'pensionInfoId',
            salaryEntry: 'salaryEntryId',
            // Certified entities (for compatibility)
            certifiedData: 'certifiedDataId',
            certifiedPensionInfo: 'certifiedPensionInfoId',
            certifiedEmploymentInfo: 'certifiedEmploymentInfoId',
            certifiedPersonalInfo: 'certifiedPersonalInfoId',
            certifiedIndexationStartOfYear: 'certifiedIndexationStartOfYearId',
            certifiedPensionCorrections: 'certifiedPensionCorrectionsId',
            certifiedVoluntaryContributions:
                'certifiedVoluntaryContributionsId',
            certifiedPensionParameters: 'certifiedPensionParametersId',
            certifiedAddress: 'certifiedAddressId',
            certifiedChild: 'certifiedChildId',
            certifiedPartnerInfo: 'certifiedPartnerInfoId',
            certifiedSalaryEntry: 'certifiedSalaryEntryId',
        }

        const modelName = entityModelMap[entityType]
        if (!modelName) {
            throw new BadRequestException(
                `Unsupported entity type: ${entityType}`
            )
        }

        const foreignKeyField = foreignKeyMap[entityType]
        if (!foreignKeyField) {
            throw new BadRequestException(
                `No foreign key mapping for entity type: ${entityType}`
            )
        }

        // Find the entity
        const entity = await this.prisma[modelName].findUnique({
            where: { id: entityId },
        })

        if (!entity) {
            throw new NotFoundException(
                `${entityType} with ID ${entityId} not found`
            )
        }

        await this.prisma.$transaction(async (prisma) => {
            // Add the field to pendingChanges if it's not already there
            const currentPendingChanges = entity.pendingChanges || []
            const updatedPendingChanges = currentPendingChanges.includes(
                fieldName
            )
                ? currentPendingChanges
                : [...currentPendingChanges, fieldName]

            // Update the entity with the pending changes
            await prisma[modelName].update({
                where: { id: entityId },
                data: {
                    pendingChanges: updatedPendingChanges,
                },
            })

            // Create the reject reason
            await prisma.certificationRejectReason.create({
                data: {
                    field: fieldName,
                    reason: rejectReason,
                    status: 'VALID',
                    [foreignKeyField]: entityId,
                },
            })
        })

        return {
            success: true,
            entityId,
            entityType,
            fieldName,
            message: `Field "${fieldName}" rejected successfully for ${entityType}`,
        }
    }
}
