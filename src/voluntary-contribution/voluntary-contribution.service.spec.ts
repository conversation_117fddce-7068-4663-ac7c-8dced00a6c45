import { Test, TestingModule } from '@nestjs/testing'
import { VoluntaryContributionService } from './voluntary-contribution.service'

describe('VoluntaryContributionService', () => {
    let service: VoluntaryContributionService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [VoluntaryContributionService],
        }).compile()

        service = module.get<VoluntaryContributionService>(
            VoluntaryContributionService
        )
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})
