import { Test, TestingModule } from '@nestjs/testing'
import { VoluntaryContributionResolver } from './voluntary-contribution.resolver'
import { VoluntaryContributionService } from './voluntary-contribution.service'

describe('VoluntaryContributionResolver', () => {
    let resolver: VoluntaryContributionResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                VoluntaryContributionResolver,
                VoluntaryContributionService,
            ],
        }).compile()

        resolver = module.get<VoluntaryContributionResolver>(
            VoluntaryContributionResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
