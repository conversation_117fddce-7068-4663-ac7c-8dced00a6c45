import { Test, TestingModule } from '@nestjs/testing'
import { AnnualAccrualResolver } from './annual-accrual.resolver'
import { AnnualAccrualService } from './annual-accrual.service'

describe('AnnualAccrualResolver', () => {
    let resolver: AnnualAccrualResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [AnnualAccrualResolver, AnnualAccrualService],
        }).compile()

        resolver = module.get<AnnualAccrualResolver>(AnnualAccrualResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
