import { Test, TestingModule } from '@nestjs/testing'
import { ConversionDetailsResolver } from './conversion-details.resolver'
import { ConversionDetailsService } from './conversion-details.service'

describe('ConversionDetailsResolver', () => {
    let resolver: ConversionDetailsResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [ConversionDetailsResolver, ConversionDetailsService],
        }).compile()

        resolver = module.get<ConversionDetailsResolver>(
            ConversionDetailsResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
