import { ObjectType, Field, ID } from '@nestjs/graphql'
import { CertifiedData } from '../../certified-data/entities/certified-data.entity'
import { CertificationRejectReason } from '../../certification-reject-reason/entities/certification-reject-reason.entity'

@ObjectType()
export class CertifiedPartnerInfo {
    @Field(() => ID)
    id: string

    @Field(() => CertifiedData)
    certifiedData: CertifiedData

    @Field()
    certifiedDataId: string

    @Field()
    isCurrent: boolean

    @Field({ nullable: true })
    firstName?: string

    @Field({ nullable: true })
    lastName?: string

    @Field(() => Date, { nullable: true })
    dateOfBirth?: Date

    @Field()
    isDeceased: boolean

    @Field(() => Date, { nullable: true })
    startDate?: Date

    @Field(() => [String], { nullable: true })
    pendingChanges?: string[]

    @Field(() => [String], { nullable: true })
    requestedChanges?: string[]

    @Field(() => [String], { nullable: true })
    approvedChanges?: string[]

    @Field(() => [CertificationRejectReason], { nullable: true })
    certificationRejectReason?: CertificationRejectReason[]

    @Field(() => [String], { nullable: true })
    differences?: string[]
}
