import {
    Injectable,
    NotFoundException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateNotificationInput } from './dto/create-notification.input'
import { UpdateNotificationInput } from './dto/update-notification.input'
import { PusherService } from '../pusher/pusher.service'

@Injectable()
export class NotificationService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly pusherService: PusherService
    ) {}

    private readonly notificationInclude = {
        createdBy: true,
        recipient: true,
    }

    async create(
        createNotificationInput: CreateNotificationInput,
        currentUserId: string
    ) {
        const { recipientId, message, type, entityId, entityType } =
            createNotificationInput

        const recipientExists = await this.prisma.user.findUnique({
            where: { id: recipientId },
        })

        if (!recipientExists) {
            throw new NotFoundException(
                `Recipient with id ${recipientId} not found`
            )
        }

        const notification = await this.prisma.notification.create({
            data: {
                message,
                type,
                entityId,
                entityType,
                createdBy: {
                    connect: { id: currentUserId },
                },
                recipient: {
                    connect: { id: recipientId },
                },
            },
            include: this.notificationInclude,
        })

        // Publish notification to Pusher
        try {
            await this.pusherService.publishToUserChannel(
                recipientId,
                'notification.created',
                {
                    id: notification.id,
                    message: notification.message,
                    type: notification.type,
                    entityId: notification.entityId,
                    entityType: notification.entityType,
                    createdAt: notification.createdAt,
                    read: notification.read,
                    createdBy: notification.createdBy,
                }
            )
        } catch (error) {
            // Log error but don't fail the notification creation
            console.error('Failed to publish notification to Pusher:', error)
        }

        return notification
    }

    async findAll(userId: string) {
        return this.prisma.notification.findMany({
            where: {
                recipientId: userId,
            },
            include: this.notificationInclude,
            orderBy: {
                createdAt: 'desc',
            },
        })
    }

    async findUnread(userId: string) {
        return this.prisma.notification.findMany({
            where: {
                recipientId: userId,
                read: false,
            },
            include: this.notificationInclude,
            orderBy: {
                createdAt: 'desc',
            },
        })
    }

    async findOne(id: string, userId: string) {
        const notification = await this.prisma.notification.findUnique({
            where: { id },
            include: this.notificationInclude,
        })

        if (!notification) {
            throw new NotFoundException(`Notification with id ${id} not found`)
        }

        // if (notification.recipientId !== userId) {
        //     throw new ForbiddenException(
        //         'You do not have permission to access this notification'
        //     )
        // }

        return notification
    }

    async update(
        id: string,
        updateNotificationInput: UpdateNotificationInput,
        _userId: string
    ) {
        await this.findOne(id, _userId)

        const updateData: any = { ...updateNotificationInput }
        delete updateData.id

        if (updateData.read === true) {
            updateData.readAt = new Date()
        }

        return this.prisma.notification.update({
            where: { id },
            data: updateData,
            include: this.notificationInclude,
        })
    }

    async markAsRead(id: string, userId: string) {
        await this.findOne(id, userId)

        const updatedNotification = await this.prisma.notification.update({
            where: { id },
            data: {
                read: true,
                readAt: new Date(),
            },
            include: this.notificationInclude,
        })

        // Publish read status update to Pusher
        try {
            await this.pusherService.publishToUserChannel(
                userId,
                'notification.read',
                {
                    id: updatedNotification.id,
                    read: true,
                    readAt: updatedNotification.readAt,
                }
            )
        } catch (error) {
            console.error(
                'Failed to publish notification read status to Pusher:',
                error
            )
        }

        return updatedNotification
    }

    async markAllAsRead(userId: string) {
        const result = await this.prisma.notification.updateMany({
            where: {
                recipientId: userId,
                read: false,
            },
            data: {
                read: true,
                readAt: new Date(),
            },
        })

        // Publish bulk read status update to Pusher
        try {
            await this.pusherService.publishToUserChannel(
                userId,
                'notification.bulk_read',
                {
                    count: result.count,
                    readAt: new Date(),
                }
            )
        } catch (error) {
            console.error(
                'Failed to publish bulk notification read status to Pusher:',
                error
            )
        }

        return result
    }

    async remove(id: string, userId: string) {
        // First check if the notification exists and belongs to the user
        await this.findOne(id, userId)

        return this.prisma.notification.delete({
            where: { id },
            include: this.notificationInclude,
        })
    }

    async countUnread(userId: string) {
        return this.prisma.notification.count({
            where: {
                recipientId: userId,
                read: false,
            },
        })
    }
}
