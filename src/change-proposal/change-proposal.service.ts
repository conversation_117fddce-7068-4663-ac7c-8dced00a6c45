import {
    BadRequestException,
    Injectable,
    NotFoundException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateChangeProposalInput } from './dto/create-change-proposal.input'
import { UpdateChangeProposalInput } from './dto/update-change-proposal.input'
import { ChangeStatus, ChangeType } from '@prisma/client'
import { CreateChangeDataInput } from '../change-data/dto/create-change-data.input'
import { PersonalInfoService } from '../personal-info/personal-info.service'
import { PartnerInfoService } from '../partner-info/partner-info.service'
import { AddressService } from '../address/address.service'
import { ChildService } from '../child/child.service'
import { ChangeProposal } from './entities/change-proposal.entity'
import { ChangeData } from '../change-data/entities/change-data.entity'
import { PensionParametersService } from '../pension-parameters/pension-parameters.service'
import { SalaryEntryService } from '../salary-entry/salary-entry.service'
import { PensionInfoService } from '../pension-info/pension-info.service'
import { CertifiedPersonalInfoService } from '../certified-personal-info/certified-personal-info.service'
import { CertifiedPensionInfoService } from '../certified-pension-info/certified-pension-info.service'
import { CertifiedPensionParametersService } from '../certified-pension-parameters/certified-pension-parameters.service'
import { CertifiedEmploymentInfoService } from '../certified-employment-info/certified-employment-info.service'
import { CertifiedIndexationStartOfYearService } from '../certified-indexation-start-of-year/certified-indexation-start-of-year.service'
import { CertifiedPensionCorrectionsService } from '../certified-pension-corrections/certified-pension-corrections.service'
import { CertifiedVoluntaryContributionsService } from '../certified-voluntary-contributions/certified-voluntary-contributions.service'
import { EmploymentInfoService } from '../employment-info/employment-info.service'
import { CertificationRejectReasonService } from '../certification-reject-reason/certification-reject-reason.service'
import { CertifiedChildService } from '../certified-child/certified-child.service'
import { CertifiedPartnerInfoService } from '../certified-partner-info/certified-partner-info.service'
import { CertifiedAddressService } from '../certified-address/certified-address.service'
import {
    getPensionCodeDescription,
    getPensionCodeImpact,
} from '../pension-info/constants/pension-codes'

@Injectable()
export class ChangeProposalService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly personalInfoService: PersonalInfoService,
        private readonly partnerInfoService: PartnerInfoService,
        private readonly addressService: AddressService,
        private readonly childService: ChildService,
        private readonly employmentInfoService: EmploymentInfoService,
        private readonly pensionParameterService: PensionParametersService,
        private readonly salaryEntryService: SalaryEntryService,
        private readonly pensionInfoService: PensionInfoService,
        private readonly certifiedPersonalInfoService: CertifiedPersonalInfoService,
        private readonly certifiedPartnerInfoService: CertifiedPartnerInfoService,
        private readonly certifiedAddressService: CertifiedAddressService,
        private readonly certifiedPensionInfoService: CertifiedPensionInfoService,
        private readonly certifiedPensionParametersService: CertifiedPensionParametersService,
        private readonly certifiedEmploymentInfoService: CertifiedEmploymentInfoService,
        private readonly certifiedIndexationStartOfYearService: CertifiedIndexationStartOfYearService,
        private readonly certifiedPensionCorrectionsService: CertifiedPensionCorrectionsService,
        private readonly certifiedVoluntaryContributionsService: CertifiedVoluntaryContributionsService,
        private readonly certificationRejectReasonService: CertificationRejectReasonService,
        private readonly certifiedChildService: CertifiedChildService
    ) {}

    async create(
        createChangeProposalInput: CreateChangeProposalInput,
        createChangeDataInput?: CreateChangeDataInput
    ) {
        const changeProposal = await this.prisma.changeProposal.create({
            data: {
                changes: {
                    create: {
                        ...createChangeDataInput,
                    },
                },
                ...createChangeProposalInput,
            },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })

        if (!changeProposal) {
            throw new NotFoundException('ChangeProposal not created')
        }

        await this.handlePendingChanges(
            createChangeProposalInput,
            createChangeDataInput
        )

        // Mark any existing certification reject reasons for this field as submitted for review
        await this.markRejectReasonsAsSubmitted(
            createChangeProposalInput,
            createChangeDataInput
        )

        return changeProposal
    }

    private async markRejectReasonsAsSubmitted(
        createChangeProposalInput: CreateChangeProposalInput,
        createChangeDataInput: CreateChangeDataInput
    ): Promise<void> {
        const { entityType, entityId } = createChangeProposalInput
        const { path } = createChangeDataInput

        // Map entity types to their foreign key field names in CertificationRejectReason
        const entityTypeToForeignKeyMap = {
            // Regular entities
            PersonalInfo: 'personalInfoId',
            PartnerInfo: 'partnerInfoId',
            Address: 'addressId',
            Child: 'childId',
            EmploymentInfo: 'employmentInfoId',
            PensionInfo: 'pensionInfoId',
            SalaryEntry: 'salaryEntryId',

            // Certified entities
            CertifiedData: 'certifiedDataId',
            CertifiedPersonalInfo: 'certifiedPersonalInfoId',
            CertifiedPartnerInfo: 'certifiedPartnerInfoId',
            CertifiedAddress: 'certifiedAddressId',
            CertifiedChild: 'certifiedChildId',
            CertifiedEmploymentInfo: 'certifiedEmploymentInfoId',
            CertifiedPensionInfo: 'certifiedPensionInfoId',
            CertifiedSalaryEntry: 'certifiedSalaryEntryId',
            CertifiedIndexationStartOfYear: 'certifiedIndexationStartOfYearId',
            CertifiedPensionCorrections: 'certifiedPensionCorrectionsId',
            CertifiedVoluntaryContributions:
                'certifiedVoluntaryContributionsId',
            CertifiedPensionParameters: 'certifiedPensionParametersId',
        }

        const foreignKeyField = entityTypeToForeignKeyMap[entityType]

        if (foreignKeyField) {
            try {
                // Update any existing certification reject reasons for this field
                await this.prisma.certificationRejectReason.updateMany({
                    where: {
                        [foreignKeyField]: entityId,
                        field: path,
                        status: 'VALID', // Only update valid reject reasons
                        submittedForReview: false, // Only update if not already submitted
                    } as any,
                    data: {
                        submittedForReview: true,
                        submittedAt: new Date(),
                    } as any,
                })
            } catch (error) {
                console.error(
                    'Error marking reject reasons as submitted:',
                    error
                )
                // Don't throw - this shouldn't break the change proposal creation
            }
        }
    }

    private async handlePendingChanges(
        createChangeProposalInput: CreateChangeProposalInput,
        createChangeDataInput: CreateChangeDataInput
    ): Promise<void> {
        const { type, entityId } = createChangeProposalInput
        const { path } = createChangeDataInput

        switch (type) {
            case ChangeType.PARTICIPANT:
                await this.handleParticipantChanges(
                    createChangeProposalInput.entityType,
                    entityId,
                    path
                )
                break
            case ChangeType.CERTIFIED_DATA:
                await this.handleCertifiedDataChanges(
                    createChangeProposalInput.entityType,
                    entityId,
                    path
                )
                break

            case ChangeType.PARAMETERS:
                await this.pensionParameterService.updatePendingChanges(
                    entityId,
                    [path]
                )
                break

            // Add other change types as needed
            default:
                // Optionally log or handle unknown change types
                break
        }
    }

    private async handleParticipantChanges(
        entityType: string,
        entityId: string,
        path: string
    ): Promise<void> {
        // Map entity types to their corresponding services
        const serviceMap = {
            PartnerInfo: this.partnerInfoService,
            PersonalInfo: this.personalInfoService,
            EmploymentInfo: this.employmentInfoService,
            Address: this.addressService,
            Child: this.childService,
            SalaryEntry: this.salaryEntryService,
            PensionInfo: this.pensionInfoService,
        }

        const service = serviceMap[entityType]

        if (service && typeof service.updatePendingChanges === 'function') {
            await service.updatePendingChanges(entityId, [path])
        } else {
            // Optionally log unknown entity types or throw an error
            console.warn(
                `Unknown entity type for participant changes: ${entityType}`
            )
        }
    }

    private async handleCertifiedDataChanges(
        entityType: string,
        entityId: string,
        path: string
    ): Promise<void> {
        const serviceMap = {
            CertifiedPersonalInfo: this.certifiedPersonalInfoService,
            CertifiedPensionInfo: this.certifiedPensionInfoService,
            CertifiedPensionParameters: this.certifiedPensionParametersService,
            CertifiedEmploymentInfo: this.certifiedEmploymentInfoService,
            CertifiedIndexationStartOfYear:
                this.certifiedIndexationStartOfYearService,
            CertifiedPensionCorrections:
                this.certifiedPensionCorrectionsService,
            CertifiedVoluntaryContributions:
                this.certifiedVoluntaryContributionsService,
            CertifiedChild: this.certifiedChildService,
            CertifiedPartnerInfo: this.certifiedPartnerInfoService,
            CertifiedAddress: this.certifiedAddressService,
        }

        const service = serviceMap[entityType]

        if (service) {
            if (typeof service.updatePendingChanges !== 'function') {
                await this.updateCertifiedPendingChanges(service, entityId, [
                    path,
                ])
            } else {
                await service.updatePendingChanges(entityId, [path])
            }
        } else {
            console.warn(
                `Unknown entity type for certified data changes: ${entityType}`
            )
        }
    }

    private async updateCertifiedPendingChanges(
        service: any,
        id: string,
        paths: string[]
    ) {
        const entity = await service.findOne(id)

        if (!entity) {
            throw new NotFoundException(`Entity with ID ${id} not found`)
        }

        const currentChanges = entity.pendingChanges || []
        const uniqueChanges = [...new Set([...currentChanges, ...paths])]

        const modelName = this.getModelNameFromService(service.constructor.name)

        if (modelName) {
            await this.prisma[modelName].update({
                where: { id },
                data: {
                    pendingChanges: uniqueChanges,
                },
            })
        } else {
            console.warn(
                `Could not determine model name for service: ${service.constructor.name}`
            )
        }
    }

    private getModelNameFromService(serviceName: string): string | null {
        const modelMap = {
            CertifiedPersonalInfoService: 'certifiedPersonalInfo',
            CertifiedPensionInfoService: 'certifiedPensionInfo',
            CertifiedPensionParametersService: 'certifiedPensionParameters',
            CertifiedEmploymentInfoService: 'certifiedEmploymentInfo',
            CertifiedIndexationStartOfYearService:
                'certifiedIndexationStartOfYear',
            CertifiedPensionCorrectionsService: 'certifiedPensionCorrections',
            CertifiedVoluntaryContributionsService:
                'certifiedVoluntaryContributions',
        }

        return modelMap[serviceName] || null
    }

    async findAll() {
        return this.prisma.changeProposal.findMany({
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })
    }

    async findByReviewerForParticipant() {
        return this.prisma.changeProposal.findMany({
            where: {
                OR: [
                    { type: ChangeType.PARTICIPANT },
                    { type: ChangeType.CERTIFIED_DATA },
                ],
                status: ChangeStatus.PENDING,
            },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })
    }
    async findByReviewerForPensionParams() {
        return this.prisma.changeProposal.findMany({
            where: {
                type: ChangeType.PARAMETERS,
                status: ChangeStatus.PENDING,
            },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })
    }
    async findByReviewerForParticipantHistory(reviewerId: string) {
        return this.prisma.changeProposal.findMany({
            where: {
                reviewedById: reviewerId,
                type: ChangeType.PARTICIPANT,
                status: {
                    not: ChangeStatus.PENDING,
                },
            },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })
    }
    async findByReviewerForPensionParamsHistory(reviewerId: string) {
        return this.prisma.changeProposal.findMany({
            where: {
                reviewedById: reviewerId,
                type: ChangeType.PARAMETERS,
                status: {
                    not: ChangeStatus.PENDING,
                },
            },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })
    }

    async findOne(id: string) {
        const changeProposal = await this.prisma.changeProposal.findUnique({
            where: { id },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })

        if (!changeProposal) {
            throw new NotFoundException('ChangeProposal not found')
        }

        return changeProposal
    }

    async update(updateChangeProposalInput: UpdateChangeProposalInput) {
        const changeProposal = await this.prisma.changeProposal.update({
            where: { id: updateChangeProposalInput.id },
            data: {
                status: updateChangeProposalInput.status,
                reviewComments: updateChangeProposalInput?.reviewComments,
            },
            include: {
                createdBy: true,
                reviewedBy: true,
                changes: true,
            },
        })

        if (!changeProposal) {
            throw new NotFoundException('ChangeProposal not updated')
        }

        return changeProposal
    }

    async approve(
        changeProposalId: string,
        reviewerId: string,
        changePropagated: boolean = false
    ) {
        const changeProposal = await this.prisma.changeProposal.findUnique({
            where: { id: changeProposalId },
            include: { changes: true },
        })

        console.log({ changeProposal })
        console.log({ changeProposalChanges: changeProposal.changes })

        if (!changeProposal) {
            throw new NotFoundException('Change proposal not found')
        }

        await this.applyChangesByType(changeProposal, changePropagated)

        // Update proposal status
        const updatedProposal = await this.prisma.changeProposal.update({
            where: { id: changeProposalId },
            data: {
                status: ChangeStatus.APPROVED,
                reviewedById: reviewerId,
                reviewedAt: new Date(),
                changePropagated: changePropagated,
            },
            include: { changes: true, createdBy: true },
        })

        // Clear pending changes
        await this.clearPendingChangesByType(changeProposal)

        // Reset submittedForReview flag for any related reject reasons
        await this.resetSubmittedForReviewFlag(changeProposal)

        return updatedProposal
    }

    private async resetSubmittedForReviewFlag(
        changeProposal: any
    ): Promise<void> {
        const { entityType, entityId } = changeProposal
        const changePaths = changeProposal.changes.map((c: any) => c.path)

        // Map entity types to their foreign key field names in CertificationRejectReason
        const entityTypeToForeignKeyMap = {
            // Regular entities
            PersonalInfo: 'personalInfoId',
            PartnerInfo: 'partnerInfoId',
            Address: 'addressId',
            Child: 'childId',
            EmploymentInfo: 'employmentInfoId',
            PensionInfo: 'pensionInfoId',
            SalaryEntry: 'salaryEntryId',

            // Certified entities
            CertifiedData: 'certifiedDataId',
            CertifiedPersonalInfo: 'certifiedPersonalInfoId',
            CertifiedPartnerInfo: 'certifiedPartnerInfoId',
            CertifiedAddress: 'certifiedAddressId',
            CertifiedChild: 'certifiedChildId',
            CertifiedEmploymentInfo: 'certifiedEmploymentInfoId',
            CertifiedPensionInfo: 'certifiedPensionInfoId',
            CertifiedSalaryEntry: 'certifiedSalaryEntryId',
            CertifiedIndexationStartOfYear: 'certifiedIndexationStartOfYearId',
            CertifiedPensionCorrections: 'certifiedPensionCorrectionsId',
            CertifiedVoluntaryContributions:
                'certifiedVoluntaryContributionsId',
            CertifiedPensionParameters: 'certifiedPensionParametersId',
        }

        const foreignKeyField = entityTypeToForeignKeyMap[entityType]

        if (foreignKeyField && changePaths.length > 0) {
            try {
                // Reset submittedForReview flag for any reject reasons related to the processed change
                await this.prisma.certificationRejectReason.updateMany({
                    where: {
                        [foreignKeyField]: entityId,
                        field: { in: changePaths },
                        submittedForReview: true,
                    } as any,
                    data: {
                        submittedForReview: false,
                        submittedAt: null,
                    } as any,
                })
            } catch (error) {
                console.error('Error resetting submittedForReview flag:', error)
                // Don't throw - this shouldn't break the change proposal processing
            }
        }
    }

    private async applyChangesByType(
        changeProposal: any,
        changePropagated: boolean = false
    ): Promise<void> {
        const changeHandlers = {
            [ChangeType.PARTICIPANT]: () =>
                this.applyChangesForParticipant(changeProposal),
            [ChangeType.PARAMETERS]: () =>
                this.applyChangesForPensionParameters(changeProposal),
            [ChangeType.CERTIFIED_DATA]: () =>
                this.applyChangesForCertifiedData(
                    changeProposal,
                    changePropagated
                ),
        }

        const handler = changeHandlers[changeProposal.type]

        if (handler) {
            await handler()
        } else {
            console.warn(`Unknown change type: ${changeProposal.type}`)
            throw new BadRequestException(
                `Unsupported change type: ${changeProposal.type}`
            )
        }
    }

    private async clearPendingChangesByType(
        changeProposal: any
    ): Promise<void> {
        const changePaths = changeProposal.changes.map((c: any) => c.path)

        const clearHandlers = {
            [ChangeType.PARTICIPANT]: () =>
                this.clearPendingChanges(
                    changeProposal.entityType,
                    changeProposal.entityId,
                    changePaths
                ),
            [ChangeType.PARAMETERS]: () =>
                this.clearPendingParameterChanges(
                    changeProposal.entityId,
                    changePaths
                ),
            [ChangeType.CERTIFIED_DATA]: () =>
                this.clearPendingCertifiedDataChanges(
                    changeProposal.entityType,
                    changeProposal.entityId,
                    changePaths
                ),
        }

        const handler = clearHandlers[changeProposal.type]

        if (handler) {
            await handler()
        } else {
            console.warn(
                `Unknown change type for clearing: ${changeProposal.type}`
            )
        }
    }

    async reject(
        changeProposalId: string,
        reviewerId: string,
        comments: string
    ) {
        const changeProposal = await this.prisma.changeProposal.findUnique({
            where: { id: changeProposalId },
            include: { changes: true },
        })

        const updatedProposal = await this.prisma.changeProposal.update({
            where: { id: changeProposalId },
            data: {
                status: ChangeStatus.REJECTED,
                reviewedById: reviewerId,
                reviewedAt: new Date(),
                reviewComments: comments,
            },
            include: { changes: true },
        })

        // Clear pending changes
        await this.clearPendingChangesByType(changeProposal)

        // Reset submittedForReview flag for any related reject reasons
        await this.resetSubmittedForReviewFlag(changeProposal)

        // For certified data changes, invalidate reject reasons and update arrays
        if (
            changeProposal.type === ChangeType.CERTIFIED_DATA &&
            changeProposal.entityType.startsWith('Certified')
        ) {
            const changes = changeProposal.changes[0]
            const entityTypeForRejectReason =
                changeProposal.entityType.charAt(0).toLowerCase() +
                changeProposal.entityType.slice(1)

            // Invalidate any reject reasons for this field
            await this.certificationRejectReasonService.invalidateRejectReasonsForField(
                changeProposal.entityId,
                changes.path,
                entityTypeForRejectReason
            )

            // Get the appropriate service
            const serviceMap = {
                CertifiedPersonalInfo: this.certifiedPersonalInfoService,
                CertifiedPensionInfo: this.certifiedPensionInfoService,
                CertifiedPensionParameters:
                    this.certifiedPensionParametersService,
                CertifiedEmploymentInfo: this.certifiedEmploymentInfoService,
                CertifiedIndexationStartOfYear:
                    this.certifiedIndexationStartOfYearService,
                CertifiedPensionCorrections:
                    this.certifiedPensionCorrectionsService,
                CertifiedVoluntaryContributions:
                    this.certifiedVoluntaryContributionsService,
            }

            const service = serviceMap[changeProposal.entityType]

            if (service) {
                // Update the entity to remove the field from requestedChanges
                const entity = await service.findOne(changeProposal.entityId)
                if (entity) {
                    const currentRequestedChanges =
                        entity.requestedChanges || []

                    // Remove the field from requestedChanges
                    const updatedRequestedChanges =
                        currentRequestedChanges.filter(
                            (field: string) => field !== changes.path
                        )

                    // Get the model name for the entity
                    const modelName = this.getModelNameFromService(
                        service.constructor.name
                    )

                    if (modelName) {
                        await this.prisma[modelName].update({
                            where: { id: changeProposal.entityId },
                            data: {
                                requestedChanges: updatedRequestedChanges,
                            },
                        })
                    }
                }
            }
        }

        return updatedProposal
    }

    private async applyChangesForParticipant(changeProposal: any) {
        const changes = changeProposal.changes[0]
        const effectiveDate = changeProposal.effectiveDate

        const updateChangeDetails = {
            entityId: changeProposal.entityId,
            path: changes.path,
            newValue: changes.newValue,
            oldValue: changes.oldValue,
        }

        switch (changeProposal.entityType) {
            case 'PartnerInfo':
                await this.partnerInfoService.changeUpdate(updateChangeDetails)
                break
            case 'PersonalInfo':
                await this.personalInfoService.changeUpdate(updateChangeDetails)
                break
            case 'EmploymentInfo':
                await this.employmentInfoService.changeUpdate(
                    updateChangeDetails
                )
                break
            case 'Address':
                await this.addressService.changeUpdate(updateChangeDetails)
                break
            case 'Child':
                await this.childService.changeUpdate(updateChangeDetails)
                break
            case 'SalaryEntry':
                await this.salaryEntryService.changeUpdate(updateChangeDetails)
                break
            case 'PensionInfo':
                // Special handling for PensionInfo code changes
                if (changes.path === 'code') {
                    const currentPensionInfo =
                        await this.prisma.pensionInfo.findUnique({
                            where: { id: changeProposal.entityId },
                        })

                    const participantId = currentPensionInfo.participantId

                    console.log({
                        previousCode: currentPensionInfo.participantId,
                        newValue: +changes.newValue,
                        codeDescription: getPensionCodeDescription(
                            +changes.newValue
                        ),
                    })

                    if (currentPensionInfo) {
                        await this.prisma.pensionInfo.update({
                            where: { id: changeProposal.entityId },
                            data: {
                                previousCode: currentPensionInfo.code,
                                codeDescription: getPensionCodeDescription(
                                    +changes.newValue
                                ),
                                codeEffectiveDate: effectiveDate,
                                previousCodeEffectiveDate:
                                    currentPensionInfo.codeEffectiveDate,
                                codeImpact: getPensionCodeImpact(
                                    +changes.newValue
                                ),
                            },
                        })

                        if (
                            +changes.newValue === 70 ||
                            +changes.newValue === 40 ||
                            +changes.newValue === 30
                        ) {
                            await this.employmentInfoService.updateByParticipantId(
                                participantId,
                                {
                                    id: participantId,
                                    endDate: effectiveDate,
                                }
                            )
                        }
                    }
                }
                await this.pensionInfoService.changeUpdate(updateChangeDetails)
                break
            default:
                throw new BadRequestException('Unsupported entity type')
        }

        const entityTypeForRejectReason =
            changeProposal.entityType.charAt(0).toLowerCase() +
            changeProposal.entityType.slice(1)

        try {
            await this.certificationRejectReasonService.invalidateRejectReasonsForField(
                changeProposal.entityId,
                changes.path,
                entityTypeForRejectReason
            )
        } catch (error) {
            // Silently handle if no reject reasons exist for non-certified entities
            console.log(
                `No reject reasons to invalidate for ${entityTypeForRejectReason} field ${changes.path}`
            )
        }
    }

    private async applyChangesForPensionParameters(changeProposal: any) {
        const changes = changeProposal.changes[0]

        const updateChangeDetails = {
            entityId: changeProposal.entityId,
            path: changes.path,
            newValue: changes.newValue,
        }

        await this.pensionParameterService.changeUpdate(updateChangeDetails)
    }

    private async applyChangesForCertifiedData(
        changeProposal: any,
        changePropagated: boolean = false
    ) {
        const changes = changeProposal.changes[0]

        const updateChangeDetails = {
            entityId: changeProposal.entityId,
            path: changes.path,
            newValue: changes.newValue,
            oldValue: changes.oldValue,
        }

        const serviceMap = {
            CertifiedPersonalInfo: this.certifiedPersonalInfoService,
            CertifiedPartnerInfo: this.certifiedPartnerInfoService,
            CertifiedChild: this.certifiedChildService,
            CertifiedAddress: this.certifiedAddressService,
            CertifiedPensionInfo: this.certifiedPensionInfoService,
            CertifiedPensionParameters: this.certifiedPensionParametersService,
            CertifiedEmploymentInfo: this.certifiedEmploymentInfoService,
            CertifiedIndexationStartOfYear:
                this.certifiedIndexationStartOfYearService,
            CertifiedPensionCorrections:
                this.certifiedPensionCorrectionsService,
            CertifiedVoluntaryContributions:
                this.certifiedVoluntaryContributionsService,
            PersonalInfo: this.personalInfoService,
            PensionInfo: this.pensionInfoService,
            EmploymentInfo: this.employmentInfoService,
            SalaryEntry: this.salaryEntryService,
            PensionParameters: this.pensionParameterService,
            Child: this.childService,
            Address: this.addressService,
            PartnerInfo: this.partnerInfoService,
        }

        //const of entityType without `Certified`
        const propagatedEntityType = changeProposal.entityType.replace(
            'Certified',
            ''
        )

        const service = serviceMap[changeProposal.entityType]
        const propagatedService = serviceMap[propagatedEntityType]

        if (service && typeof service.changeUpdate === 'function') {
            await service.changeUpdate(updateChangeDetails)

            // After applying the change, invalidate any reject reasons for this field
            if (changeProposal.entityType.startsWith('Certified')) {
                const entityTypeForRejectReason =
                    changeProposal.entityType.charAt(0).toLowerCase() +
                    changeProposal.entityType.slice(1)
                await this.certificationRejectReasonService.invalidateRejectReasonsForField(
                    changeProposal.entityId,
                    changes.path,
                    entityTypeForRejectReason
                )

                // Update the entity to remove the field from requestedChanges and add to approvedChanges
                const entity = await service.findOne(changeProposal.entityId)
                if (entity) {
                    const currentRequestedChanges =
                        entity.requestedChanges || []
                    const currentApprovedChanges = entity.approvedChanges || []

                    // Remove the field from requestedChanges
                    const updatedRequestedChanges =
                        currentRequestedChanges.filter(
                            (field: string) => field !== changes.path
                        )

                    // Remove the field from approvedChanges
                    const updatedApprovedChanges =
                        currentApprovedChanges.filter(
                            (field: string) => field !== changes.path
                        )

                    // Get the model name for the entity
                    const modelName = this.getModelNameFromService(
                        service.constructor.name
                    )

                    if (modelName) {
                        await this.prisma[modelName].update({
                            where: { id: changeProposal.entityId },
                            data: {
                                requestedChanges: updatedRequestedChanges,
                                approvedChanges: updatedApprovedChanges,
                            },
                        })
                    }
                }
            }
        } else {
            console.warn(
                `Unknown entity type for certified data changes or missing changeUpdate method: ${changeProposal.entityType}`
            )
            throw new BadRequestException(
                `Unsupported certified data entity type: ${changeProposal.entityType}`
            )
        }

        if (changePropagated) {
            const relatedCertifiedData = await service.findOne(
                changeProposal.entityId
            )

            const participantId =
                relatedCertifiedData.certifiedData.participantId

            if (
                propagatedService &&
                typeof propagatedService.updateFieldByParticipantId ===
                    'function'
            ) {
                const updatedEntity =
                    await propagatedService.updateFieldByParticipantId({
                        participantId: participantId,
                        path: changes.path,
                        newValue: changes.newValue,
                    })
                console.log('updatedEntity', updatedEntity)
            } else {
                console.warn(
                    `Unknown entity type for certified data changes or missing updateFieldByParticipantId method: ${changeProposal.propagatedEntityType}`
                )
                throw new BadRequestException(
                    `Unsupported certified data entity type: ${changeProposal.propagatedEntityType}`
                )
            }
        }
    }

    private async clearPendingChanges(
        entityType: string,
        entityId: string,
        paths: string[]
    ) {
        switch (entityType) {
            case 'PartnerInfo':
                await this.partnerInfoService.clearPendingChanges(
                    entityId,
                    paths
                )
                break
            case 'PersonalInfo':
                await this.personalInfoService.clearPendingChanges(
                    entityId,
                    paths
                )
                break
            case 'EmploymentInfo':
                await this.employmentInfoService.clearPendingChanges(
                    entityId,
                    paths
                )
                break
            case 'Address':
                await this.addressService.clearPendingChanges(entityId, paths)
                break
            case 'Child':
                await this.childService.clearPendingChanges(entityId, paths)
                break
            case 'SalaryEntry':
                await this.salaryEntryService.clearPendingChanges(
                    entityId,
                    paths
                )
                break
            case 'PensionInfo':
                await this.pensionInfoService.clearPendingChanges(
                    entityId,
                    paths
                )
                break
            default:
                throw new BadRequestException('Unsupported entity type')
        }
    }

    private async clearPendingParameterChanges(
        entityId: string,
        paths: string[]
    ) {
        await this.pensionParameterService.clearPendingParameterChanges(
            entityId,
            paths
        )
    }

    private async clearPendingCertifiedDataChanges(
        entityType: string,
        entityId: string,
        paths: string[]
    ) {
        const serviceMap = {
            CertifiedPersonalInfo: this.certifiedPersonalInfoService,
            CertifiedPartnerInfo: this.certifiedPartnerInfoService,
            CertifiedAddress: this.certifiedAddressService,
            CertifiedChild: this.certifiedChildService,
            CertifiedPensionInfo: this.certifiedPensionInfoService,
            CertifiedPensionParameters: this.certifiedPensionParametersService,
            CertifiedEmploymentInfo: this.certifiedEmploymentInfoService,
            CertifiedIndexationStartOfYear:
                this.certifiedIndexationStartOfYearService,
            CertifiedPensionCorrections:
                this.certifiedPensionCorrectionsService,
            CertifiedVoluntaryContributions:
                this.certifiedVoluntaryContributionsService,
        }

        const service = serviceMap[entityType]

        if (service) {
            if (typeof service.clearPendingChanges === 'function') {
                await service.clearPendingChanges(entityId, paths)
            } else {
                // Use a generic approach if the service doesn't have a clearPendingChanges method
                const entity = await service.findOne(entityId)

                if (!entity) {
                    throw new NotFoundException(
                        `Entity with ID ${entityId} not found`
                    )
                }

                const currentChanges = entity.pendingChanges || []
                const updatedChanges = currentChanges.filter(
                    (path: string) => !paths.includes(path)
                )

                const modelName = this.getModelNameFromService(
                    service.constructor.name
                )

                if (modelName) {
                    await this.prisma[modelName].update({
                        where: { id: entityId },
                        data: { pendingChanges: updatedChanges },
                    })
                } else {
                    console.warn(
                        `Could not determine model name for service: ${service.constructor.name}`
                    )
                }
            }
        } else {
            console.warn(
                `Unknown entity type for certified data changes: ${entityType}`
            )
            throw new BadRequestException(
                `Unsupported certified data entity type: ${entityType}`
            )
        }
    }

    //Get latest approved change for provided entityType eg: PersonalInfo based on reviewedAt whose changeData.path is provided eg: firstName
    async getLatestApprovedChange(entityType: string, path: string) {
        const changeProposal = await this.prisma.changeProposal.findFirst({
            where: {
                type: ChangeType.PARTICIPANT,
                status: ChangeStatus.APPROVED,
                entityType: entityType,
                changes: {
                    some: {
                        path,
                    },
                },
            },

            orderBy: {
                reviewedAt: 'desc',
            },
            include: {
                changes: true,
                createdBy: true,
                reviewedBy: true,
            },
        })
        console.log({ changeProposal })

        return changeProposal
    }

    async delete(id: string) {
        const changeProposal = await this.prisma.changeProposal.delete({
            where: { id },
        })

        if (!changeProposal) {
            throw new NotFoundException('ChangeProposal not deleted')
        }

        return changeProposal
    }
}
