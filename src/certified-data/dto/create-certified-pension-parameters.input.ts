import {
    InputType,
    Field,
    Float,
    Int,
    GraphQLISODateTime,
} from '@nestjs/graphql'
import {
    IsNumber,
    IsOptional,
    IsString,
    IsInt,
    IsDate,
    ValidateNested,
} from 'class-validator'
import { Type } from 'class-transformer'
import { CreateCertificationRejectReasonInput } from '../../certification-reject-reason/dto/create-certification-reject-reason.input'

@InputType()
export class CreateCertifiedPensionParametersInput {
    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    accrualPercentage?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    annualMultiplier?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    offsetAmount?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    partnersPensionPercentage?: number

    @Field(() => Int, { nullable: true })
    @IsInt()
    @IsOptional()
    retirementAge?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    voluntaryContributionInterestRate?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    year?: string

    @Field(() => GraphQLISODateTime, { nullable: true })
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    effectiveDate?: Date

    @Field(() => [CreateCertificationRejectReasonInput], { nullable: true })
    @ValidateNested({ each: true })
    @Type(() => CreateCertificationRejectReasonInput)
    @IsOptional()
    certificationRejectReason?: CreateCertificationRejectReasonInput[]
}
