/**
 * Utility functions for handling date conversions
 */

/**
 * Converts any date string format to ISO string format required by Prisma
 * @param dateStr The date string to convert
 * @returns ISO formatted date string or null if invalid
 */
export function toISODateString(dateStr: string | Date | null): string | null {
    if (!dateStr) return null

    try {
        // If already a Date object
        if (dateStr instanceof Date) {
            return dateStr.toISOString()
        }

        // Try parsing the string date
        const date = new Date(dateStr)

        // Check if valid date
        if (isNaN(date.getTime())) {
            return null
        }

        return date.toISOString()
    } catch (error) {
        console.error('Error converting date to ISO format:', error)
        return null
    }
}

/**
 * Safely converts any date input to a format acceptable by Prisma
 * @param value The date value to convert
 * @returns A Prisma-compatible date string or null
 */
export function toPrismaDate(value: any): string | null {
    if (!value) return null

    // If already a Date object
    if (value instanceof Date) {
        return value.toISOString()
    }

    // If it's a string, try to convert it
    if (typeof value === 'string') {
        return toISODateString(value)
    }

    return null
}
