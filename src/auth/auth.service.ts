import {
    Injectable,
    NotFoundException,
    UnauthorizedException,
} from '@nestjs/common'
import { LoginRequestDto, RegisterDto } from './authTypes/input-dto'
import { PrismaService } from '../prisma.service'
import { admin } from './firebase-admin.module'
import { ConfigService } from '@nestjs/config'
import { EmailHandlerService } from '../email-handler/email-handler.service'

@Injectable()
export class AuthService {
    constructor(
        private readonly emailService: EmailHandlerService,
        private readonly configService: ConfigService,
        private readonly prisma: PrismaService
    ) {}

    private userType = {
        reviewer: 'reviewer',
        editor: 'editor',
        admin: 'admin',
    }

    async verifyToken(LoginRequestDto: LoginRequestDto) {
        try {
            const decodedToken = await admin
                .auth()
                .verifyIdToken(LoginRequestDto.token)
            return { user: decodedToken }
        } catch (error) {
            throw new UnauthorizedException()
        }
    }

    async handleResetPasswordEmail(email: string) {
        const resetLink = await this.createResetLink(email)
        await this.sendResetPasswordEmail(email, resetLink)
        return resetLink
    }

    async createResetLink(email: string): Promise<string> {
        const pensionAdminHost = this.configService.get('GLOBAL.FRONT_HOST')
        const pensionAdminDomain = new URL(`${pensionAdminHost}/reset-password`)
        // Generate password reset link from Firebase Auth
        const firebaseResetUrl = await admin
            .auth()
            .generatePasswordResetLink(email, {
                url: `${pensionAdminHost}/reset-password`,
            })

        const urlObject = new URL(firebaseResetUrl)

        const params = urlObject.searchParams

        params.forEach((value, key) => {
            pensionAdminDomain.searchParams.set(key, value)
        })

        return pensionAdminDomain.toString()
    }
    async createEmailVerifyLink(email: string): Promise<string> {
        const host = this.configService.get('GLOBAL.BRIDGE_FRONT_HOST')
        // Generate password reset link from Firebase Auth
        return await admin.auth().generateEmailVerificationLink(email, {
            url: `${host}`,
        })
    }

    async sendResetPasswordEmail(email: string, resetLink: string) {
        await this.emailService.sendResetPasswordEmail({
            email: email,
            resetLink: resetLink,
        })
    }

    async setUserClaims(registerDto: RegisterDto) {
        const firebaseUID = registerDto.firebaseUid

        const existingUser = await this.prisma.user.findUnique({
            where: { firebaseUid: firebaseUID },
            include: {
                role: true,
            },
        })

        if (!existingUser) {
            throw new NotFoundException(
                'User not found, Please register user first.'
            )
        }

        const userMetaData = {
            role: existingUser.role.name.toLowerCase(),
            roleId: existingUser.roleId,
            email: existingUser.email,
            pensionUserId: existingUser.id,
        }

        try {
            // Set User Claims
            await admin.auth().setCustomUserClaims(firebaseUID, {
                ...userMetaData,
            })

            console.log('Custom claims set successfully')

            // Verify Custom Claims
            const user = await admin.auth().getUser(firebaseUID)
            console.log('Custom Claims:', user.customClaims)

            // Force token refresh
            await admin.auth().revokeRefreshTokens(firebaseUID)
            console.log(
                'Tokens revoked, user will need to sign in again to get the new claims.'
            )
        } catch (error) {
            console.error('Error setting custom claims:', error)
            throw new Error(`Failed to set custom claims: ${error.message}`)
        }

        return { claims: { ...userMetaData } }
    }
}
